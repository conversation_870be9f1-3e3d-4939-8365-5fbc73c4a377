use ort::{Environment, GraphOptimization<PERSON><PERSON>l, SessionBuilder, Value};
use tokenizers::Tokenizer;
use std::sync::Arc;

fn main() -> ort::OrtResult<()> {
    // Initialize the ONNX Runtime environment
    let environment = Arc::new(Environment::builder().with_name("reranker").build()?);

    // Load our model
    let session = SessionBuilder::new(&environment)?
        .with_optimization_level(GraphOptimizationLevel::Level1)?
        .with_intra_threads(1)?
        .with_model_from_file("model.onnx")?;

    // Print model input and output information
    println!("Model inputs:");
    for (i, input) in session.inputs.iter().enumerate() {
        println!("  {}: {} (type: {:?})", i, input.name, input.input_type);
    }
    println!("Model outputs:");
    for (i, output) in session.outputs.iter().enumerate() {
        println!("  {}: {} (type: {:?})", i, output.name, output.output_type);
    }

    let tokenizer = Tokenizer::from_file("tokenizer.json").unwrap();
    let query = "What is the capital of France?";
    let passage = "Paris is the capital of France.";

    let encoding = tokenizer.encode((query, passage), true).unwrap();
    let input_ids = encoding.get_ids();
    let attention_mask = encoding.get_attention_mask();
    let token_type_ids = encoding.get_type_ids();

    // Create tensors directly using the shape and data
    let input_ids_data: Vec<i64> = input_ids.iter().map(|&x| x as i64).collect();
    let attention_mask_data: Vec<i64> = attention_mask.iter().map(|&x| x as i64).collect();
    let token_type_ids_data: Vec<i64> = token_type_ids.iter().map(|&x| x as i64).collect();

    // Create ndarray arrays with dynamic dimensions
    let input_ids_array = ndarray::Array::from_shape_vec(ndarray::IxDyn(&[1, input_ids.len()]), input_ids_data).unwrap();
    let attention_mask_array = ndarray::Array::from_shape_vec(ndarray::IxDyn(&[1, attention_mask.len()]), attention_mask_data).unwrap();
    let token_type_ids_array = ndarray::Array::from_shape_vec(ndarray::IxDyn(&[1, token_type_ids.len()]), token_type_ids_data).unwrap();

    // Convert to CowArray for ORT 1.16 compatibility
    let input_ids_cow = ndarray::CowArray::from(&input_ids_array);
    let attention_mask_cow = ndarray::CowArray::from(&attention_mask_array);
    let token_type_ids_cow = ndarray::CowArray::from(&token_type_ids_array);

    let input_ids_value = Value::from_array(session.allocator(), &input_ids_cow)?;
    let attention_mask_value = Value::from_array(session.allocator(), &attention_mask_cow)?;
    let token_type_ids_value = Value::from_array(session.allocator(), &token_type_ids_cow)?;

    // Create named inputs for the model
    let mut inputs = std::collections::HashMap::new();
    inputs.insert("input_ids", input_ids_value);
    inputs.insert("attention_mask", attention_mask_value);
    inputs.insert("token_type_ids", token_type_ids_value);

    // Run the model
    let outputs = session.run(inputs)?;

    // Access the 'logits' output
    let output_tensor = outputs[0].try_extract::<f32>()?;
    let score = output_tensor.view()[[0, 0]]; // Access the first element of the tensor data

    println!("\nQuery: {}", query);
    println!("Passage: {}", passage);
    println!("Reranking Score: {:.4}", score);

    Ok(())
}


