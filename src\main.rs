use ort::{session::{builder::GraphOptimizationLevel, Session}, value::Value};
use tokenizers::Tokenizer;

fn main() -> ort::Result<()> {
    // Load our model using the new Session::builder() API
    let mut session = Session::builder()?
        .with_optimization_level(GraphOptimizationLevel::Level1)?
        .with_intra_threads(1)?
        .commit_from_file("model.onnx")?;

    // Print model input and output information
    println!("Model inputs:");
    for (i, input) in session.inputs.iter().enumerate() {
        println!("  {}: {} (type: {:?})", i, input.name, input.input_type);
    }
    println!("Model outputs:");
    for (i, output) in session.outputs.iter().enumerate() {
        println!("  {}: {} (type: {:?})", i, output.name, output.output_type);
    }

    let tokenizer = Tokenizer::from_file("tokenizer.json").unwrap();
    let query = "What is the capital of France?";
    let passage = "Paris is the capital of France.";

    let encoding = tokenizer.encode((query, passage), true).unwrap();
    let input_ids = encoding.get_ids();
    let attention_mask = encoding.get_attention_mask();
    let token_type_ids = encoding.get_type_ids();

    // Create tensors using the shape and data tuple format (ort 2.0 API)
    let input_ids_data: Vec<i64> = input_ids.iter().map(|&x| x as i64).collect();
    let attention_mask_data: Vec<i64> = attention_mask.iter().map(|&x| x as i64).collect();
    let token_type_ids_data: Vec<i64> = token_type_ids.iter().map(|&x| x as i64).collect();

    // Create tensors using (shape, data) tuple format
    let input_ids_value = Value::from_array(([1, input_ids.len()], input_ids_data.into_boxed_slice()))?;
    let attention_mask_value = Value::from_array(([1, attention_mask.len()], attention_mask_data.into_boxed_slice()))?;
    let token_type_ids_value = Value::from_array(([1, token_type_ids.len()], token_type_ids_data.into_boxed_slice()))?;

    // Run the model using the new inputs! macro
    let outputs = session.run(ort::inputs![
        "input_ids" => input_ids_value,
        "attention_mask" => attention_mask_value,
        "token_type_ids" => token_type_ids_value
    ])?;

    // Access the 'logits' output
    let output_tensor = outputs[0].try_extract_array::<f32>()?;
    let score = output_tensor.view()[[0, 0]]; // Access the first element of the tensor data

    println!("\nQuery: {}", query);
    println!("Passage: {}", passage);
    println!("Reranking Score: {:.4}", score);

    Ok(())
}


